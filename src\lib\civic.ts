// CivicAuthService.ts

export interface CivicUser {
  id: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  isVerified: boolean;
  verificationLevel: 'basic' | 'kyc' | 'professional';
  verifiedAt: Date;
}

// Civic authentication service
export class CivicAuthService {
  private static instance: CivicAuthService;
  private currentUser: CivicUser | null = null;

  static getInstance(): CivicAuthService {
    if (!CivicAuthService.instance) {
      CivicAuthService.instance = new CivicAuthService();
    }
    return CivicAuthService.instance;
  }

  setUser(userData: {
    id: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    isVerified: boolean;
    verificationLevel?: 'basic' | 'kyc' | 'professional';
  }): CivicUser {
    const user: CivicUser = {
      id: userData.id,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      isVerified: userData.isVerified,
      verificationLevel: userData.verificationLevel || 'basic',
      verifiedAt: new Date(),
    };

    this.currentUser = user;
    this.saveUserSession(user);
    return user;
  }

  getCurrentUser(): CivicUser | null {
    if (this.currentUser) {
      return this.currentUser;
    }

    // Try to load from session storage
    const stored = this.loadUserSession();
    if (stored) {
      this.currentUser = stored;
      return stored;
    }

    return null;
  }

  async signOut(): Promise<void> {
    this.currentUser = null;
    this.clearUserSession();
  }

  private saveUserSession(user: CivicUser): void {
    try {
      const sessionData = {
        ...user,
        sessionExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
      };
      localStorage.setItem('civic_user_session', JSON.stringify(sessionData));
    } catch (error) {
      console.error('Failed to save user session:', error);
    }
  }

  private loadUserSession(): CivicUser | null {
    try {
      const stored = localStorage.getItem('civic_user_session');
      if (!stored) return null;

      const sessionData = JSON.parse(stored);
      
      // Check if session is expired
      if (new Date(sessionData.sessionExpiry) < new Date()) {
        this.clearUserSession();
        return null;
      }

      return {
        id: sessionData.id,
        email: sessionData.email,
        firstName: sessionData.firstName,
        lastName: sessionData.lastName,
        isVerified: sessionData.isVerified,
        verificationLevel: sessionData.verificationLevel,
        verifiedAt: new Date(sessionData.verifiedAt),
      };
    } catch (error) {
      console.error('Failed to load user session:', error);
      return null;
    }
  }

  private clearUserSession(): void {
    localStorage.removeItem('civic_user_session');
  }
}

export const civicAuth = CivicAuthService.getInstance();
