import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Loader2, ArrowRight } from 'lucide-react';
import { CivicUser } from '../lib/civic';
import { createSubscription } from '../lib/supabase';

interface PaymentSuccessProps {
  user: CivicUser;
  onContinue: () => void;
}

const PaymentSuccess: React.FC<PaymentSuccessProps> = ({ user, onContinue }) => {
  const [processing, setProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processPayment = async () => {
      try {
        // Get payment intent ID from URL params (Stripe redirects with this)
        const urlParams = new URLSearchParams(window.location.search);
        const paymentIntentId = urlParams.get('payment_intent');
        
        if (!paymentIntentId || !user) {
          setError('Payment verification failed. Please contact support.');
          setProcessing(false);
          return;
        }

        console.log('PaymentSuccess: Processing payment for user:', user.id, 'payment_intent:', paymentIntentId);

        // Create subscription record
        await createSubscription(user, paymentIntentId);
        
        setProcessing(false);
        
        // Auto-continue after 2 seconds
        setTimeout(() => {
          onContinue();
        }, 2000);
      } catch (err: any) {
        console.error('PaymentSuccess: Failed to process payment:', err);
        setError(err.message || 'Failed to process payment');
        setProcessing(false);
      }
    };

    if (user) {
      processPayment();
    }
  }, [user, onContinue]);

  if (processing) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-2xl mx-auto text-center"
      >
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-12 border border-gray-700">
          <Loader2 className="w-16 h-16 mx-auto mb-6 text-purple-400 animate-spin" />
          <h2 className="text-2xl font-bold text-white mb-4">Processing Your Payment</h2>
          <p className="text-gray-400">Please wait while we activate your subscription...</p>
        </div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-2xl mx-auto text-center"
      >
        <div className="bg-red-900/50 border border-red-500 rounded-xl p-12">
          <h2 className="text-2xl font-bold text-red-300 mb-4">Payment Error</h2>
          <p className="text-red-200 mb-6">{error}</p>
          <button
            onClick={() => window.location.href = '/'}
            className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            Return to Home
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-2xl mx-auto text-center"
    >
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-12 border border-gray-700">
        <CheckCircle className="w-16 h-16 mx-auto mb-6 text-green-400" />
        
        <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
          Payment Successful!
        </h2>
        
        <p className="text-xl text-gray-300 mb-8">
          Welcome to the Professional Exam Prep Platform. Your subscription is now active.
        </p>

        <div className="bg-green-600/20 border border-green-600 rounded-lg p-6 mb-8">
          <h3 className="font-semibold text-green-300 mb-2">What's Included:</h3>
          <ul className="text-green-200 text-sm space-y-1">
            <li>• 30 days of full platform access</li>
            <li>• AI-powered study strategies</li>
            <li>• Real exam questions from successful candidates</li>
            <li>• Personalized learning analytics</li>
            <li>• Multiple study modes (flashcards, multiple choice, typed answers)</li>
          </ul>
        </div>

        <button
          onClick={onContinue}
          className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg text-lg font-semibold hover:scale-105 transition-all shadow-lg flex items-center space-x-2 mx-auto"
        >
          <span>Continue to Exam Registration</span>
          <ArrowRight className="w-5 h-5" />
        </button>
      </div>
    </motion.div>
  );
};

export default PaymentSuccess;