/*
  # Update profiles table for Civic authentication

  1. Changes
    - Add wallet_address column for blockchain identity
    - Add verification_level column (basic, kyc, professional)
    - Add is_verified boolean flag
    - Add verified_at timestamp
    - Update RLS policies for Civic users
    - Remove auth.users dependency since we're using Civic

  2. Security
    - Update RLS policies to work with Civic user IDs
    - Ensure only verified users can access sensitive features
*/

-- Add new columns for Civic authentication
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'profiles' AND column_name = 'wallet_address'
  ) THEN
    ALTER TABLE profiles ADD COLUMN wallet_address text;
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'profiles' AND column_name = 'verification_level'
  ) THEN
    ALTER TABLE profiles ADD COLUMN verification_level text DEFAULT 'basic';
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'profiles' AND column_name = 'is_verified'
  ) THEN
    ALTER TABLE profiles ADD COLUMN is_verified boolean DEFAULT false;
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'profiles' AND column_name = 'verified_at'
  ) THEN
    ALTER TABLE profiles ADD COLUMN verified_at timestamptz;
  END IF;
END $$;

-- Update profiles table to not require auth.users reference
DO $$
BEGIN
  -- Remove the foreign key constraint if it exists
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'profiles_id_fkey'
  ) THEN
    ALTER TABLE profiles DROP CONSTRAINT profiles_id_fkey;
  END IF;
END $$;

-- Update RLS policies for Civic authentication
DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;

-- Create new policies that work with Civic user IDs
CREATE POLICY "Civic users can read own profile"
  ON profiles
  FOR SELECT
  TO public
  USING (true); -- We'll handle access control in the application layer

CREATE POLICY "Civic users can update own profile"
  ON profiles
  FOR UPDATE
  TO public
  USING (true);

CREATE POLICY "Civic users can insert own profile"
  ON profiles
  FOR INSERT
  TO public
  WITH CHECK (true);

-- Update other table policies to work without auth.uid()
DROP POLICY IF EXISTS "Users can read own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Users can insert own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Users can update own subscriptions" ON subscriptions;

CREATE POLICY "Civic users can read own subscriptions"
  ON subscriptions
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Civic users can insert own subscriptions"
  ON subscriptions
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Civic users can update own subscriptions"
  ON subscriptions
  FOR UPDATE
  TO public
  USING (true);

-- Update exam registrations policies
DROP POLICY IF EXISTS "Users can read own exam registrations" ON exam_registrations;
DROP POLICY IF EXISTS "Users can insert own exam registrations" ON exam_registrations;
DROP POLICY IF EXISTS "Users can update own exam registrations" ON exam_registrations;

CREATE POLICY "Civic users can read own exam registrations"
  ON exam_registrations
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Civic users can insert own exam registrations"
  ON exam_registrations
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Civic users can update own exam registrations"
  ON exam_registrations
  FOR UPDATE
  TO public
  USING (true);

-- Update study sessions policies
DROP POLICY IF EXISTS "Users can read own study sessions" ON study_sessions;
DROP POLICY IF EXISTS "Users can insert own study sessions" ON study_sessions;

CREATE POLICY "Civic users can read own study sessions"
  ON study_sessions
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Civic users can insert own study sessions"
  ON study_sessions
  FOR INSERT
  TO public
  WITH CHECK (true);

-- Update contributed questions policies
DROP POLICY IF EXISTS "Users can read verified questions" ON contributed_questions;
DROP POLICY IF EXISTS "Users can insert own contributed questions" ON contributed_questions;
DROP POLICY IF EXISTS "Users can read own contributed questions" ON contributed_questions;

CREATE POLICY "Civic users can read verified questions"
  ON contributed_questions
  FOR SELECT
  TO public
  USING (verified = true);

CREATE POLICY "Civic users can insert own contributed questions"
  ON contributed_questions
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "Civic users can read own contributed questions"
  ON contributed_questions
  FOR SELECT
  TO public
  USING (true);

-- Remove the auth trigger since we're not using Supabase auth
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user();

-- Add index for wallet addresses
CREATE INDEX IF NOT EXISTS idx_profiles_wallet_address ON profiles(wallet_address);
CREATE INDEX IF NOT EXISTS idx_profiles_verification_level ON profiles(verification_level);