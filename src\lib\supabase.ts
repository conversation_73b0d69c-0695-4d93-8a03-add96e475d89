import { createClient } from '@supabase/supabase-js';
import { Database } from './database.types';
import { CivicUser } from './civic';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase environment variables not configured. Using mock data.');
}

export const supabase = supabaseUrl && supabaseAnonKey 
  ? createClient<Database>(supabaseUrl, supabaseAnonKey)
  : null;

// Profile management for Civic users
export const createOrUpdateProfile = async (civicUser: CivicUser) => {
  if (!supabase) {
    console.log('Mock: Creating/updating profile for user:', civicUser.id);
    return { id: civicUser.id, email: civicUser.email };
  }

  try {
    const { data, error } = await supabase
      .from('profiles')
      .upsert({
        id: civicUser.id,
        email: civicUser.email || '',
        full_name: civicUser.firstName && civicUser.lastName 
          ? `${civicUser.firstName} ${civicUser.lastName}`
          : civicUser.email?.split('@')[0] || 'User',
        wallet_address: civicUser.walletAddress,
        verification_level: civicUser.verificationLevel,
        is_verified: civicUser.isVerified,
        verified_at: civicUser.verifiedAt.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Failed to create/update profile:', error);
    throw error;
  }
};

export const getProfile = async (userId: string) => {
  if (!supabase) {
    console.log('Mock: Getting profile for user:', userId);
    return { id: userId, email: '<EMAIL>' };
  }

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  } catch (error) {
    console.error('Failed to get profile:', error);
    return null;
  }
};

// Subscription helpers
export const checkUserSubscription = async (userId: string) => {
  if (!supabase) {
    console.log('Mock: Checking subscription for user:', userId);
    // Check localStorage for mock subscription
    const mockSubscription = localStorage.getItem(`subscription_${userId}`);
    if (mockSubscription) {
      const subscription = JSON.parse(mockSubscription);
      const now = new Date();
      const endDate = new Date(subscription.current_period_end);
      return now < endDate ? subscription : null;
    }
    return null;
  }

  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .gt('current_period_end', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  } catch (error) {
    console.error('Failed to check subscription:', error);
    return null;
  }
};

export const createSubscription = async (
  civicUser: CivicUser,
  stripePaymentIntentId: string
) => {
  if (!supabase) {
    console.log('Mock: Creating subscription for user:', civicUser.id, 'payment:', stripePaymentIntentId);
    // Create mock subscription in localStorage
    const subscription = {
      id: `sub_${Date.now()}`,
      user_id: civicUser.id,
      stripe_customer_id: `civic_${civicUser.id}`,
      stripe_payment_intent_id: stripePaymentIntentId,
      status: 'active',
      amount_paid: 100, // $1.00 in cents
      current_period_start: new Date().toISOString(),
      current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    localStorage.setItem(`subscription_${civicUser.id}`, JSON.stringify(subscription));
    return subscription;
  }

  try {
    // First ensure profile exists
    await createOrUpdateProfile(civicUser);
    
    const { data, error } = await supabase
      .from('subscriptions')
      .insert({
        user_id: civicUser.id,
        stripe_customer_id: `civic_${civicUser.id}`,
        stripe_payment_intent_id: stripePaymentIntentId,
        status: 'active',
        amount_paid: 100, // $1.00 in cents
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Failed to create subscription:', error);
    throw error;
  }
};

// Exam registration helpers
export const createExamRegistration = async (
  civicUser: CivicUser,
  subscriptionId: string,
  examData: {
    examTypeId: string;
    examTypeName: string;
    stateCode: string;
    stateName: string;
    examDate: string;
    studentInfo: any;
  }
) => {
  if (!supabase) {
    console.log('Mock: Creating exam registration for user:', civicUser.id);
    const registration = {
      id: `reg_${Date.now()}`,
      user_id: civicUser.id,
      subscription_id: subscriptionId,
      exam_type_id: examData.examTypeId,
      exam_type_name: examData.examTypeName,
      state_code: examData.stateCode,
      state_name: examData.stateName,
      exam_date: examData.examDate,
      exam_result: 'pending',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    localStorage.setItem(`registration_${civicUser.id}`, JSON.stringify(registration));
    return registration;
  }

  try {
    // Ensure profile exists
    await createOrUpdateProfile(civicUser);
    
    const { data, error } = await supabase
      .from('exam_registrations')
      .insert({
        user_id: civicUser.id,
        subscription_id: subscriptionId,
        exam_type_id: examData.examTypeId,
        exam_type_name: examData.examTypeName,
        state_code: examData.stateCode,
        state_name: examData.stateName,
        exam_date: examData.examDate,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Failed to create exam registration:', error);
    throw error;
  }
};

export const getUserExamRegistrations = async (userId: string) => {
  if (!supabase) {
    console.log('Mock: Getting exam registrations for user:', userId);
    const registration = localStorage.getItem(`registration_${userId}`);
    return registration ? [JSON.parse(registration)] : [];
  }

  try {
    const { data, error } = await supabase
      .from('exam_registrations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Failed to get exam registrations:', error);
    return [];
  }
};

export const updateExamResult = async (registrationId: string, result: 'pass' | 'fail') => {
  if (!supabase) {
    console.log('Mock: Updating exam result:', registrationId, result);
    return { id: registrationId, exam_result: result };
  }

  try {
    const { data, error } = await supabase
      .from('exam_registrations')
      .update({ 
        exam_result: result,
        updated_at: new Date().toISOString()
      })
      .eq('id', registrationId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Failed to update exam result:', error);
    throw error;
  }
};

// Study session helpers
export const saveStudySession = async (sessionData: {
  civicUser: CivicUser;
  registrationId: string;
  strategyType: 'flashcards' | 'multiple_choice' | 'typed_answer';
  questionsAnswered: number;
  correctAnswers: number;
  scorePercentage: number;
  timeSpentSeconds: number;
}) => {
  if (!supabase) {
    console.log('Mock: Saving study session for user:', sessionData.civicUser.id);
    const session = {
      id: `session_${Date.now()}`,
      user_id: sessionData.civicUser.id,
      registration_id: sessionData.registrationId,
      strategy_type: sessionData.strategyType,
      questions_answered: sessionData.questionsAnswered,
      correct_answers: sessionData.correctAnswers,
      score_percentage: sessionData.scorePercentage,
      time_spent_seconds: sessionData.timeSpentSeconds,
      created_at: new Date().toISOString(),
    };
    
    const sessions = JSON.parse(localStorage.getItem(`sessions_${sessionData.civicUser.id}`) || '[]');
    sessions.push(session);
    localStorage.setItem(`sessions_${sessionData.civicUser.id}`, JSON.stringify(sessions));
    return session;
  }

  try {
    // Ensure profile exists
    await createOrUpdateProfile(sessionData.civicUser);
    
    const { data, error } = await supabase
      .from('study_sessions')
      .insert({
        user_id: sessionData.civicUser.id,
        registration_id: sessionData.registrationId,
        strategy_type: sessionData.strategyType,
        questions_answered: sessionData.questionsAnswered,
        correct_answers: sessionData.correctAnswers,
        score_percentage: sessionData.scorePercentage,
        time_spent_seconds: sessionData.timeSpentSeconds,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Failed to save study session:', error);
    throw error;
  }
};

// Contributed questions helpers
export const saveContributedQuestions = async (
  civicUser: CivicUser,
  examTypeId: string,
  stateCode: string,
  questions: Array<{
    questionText: string;
    correctAnswer: string;
    explanation?: string;
    difficulty?: 'easy' | 'medium' | 'hard';
    category?: string;
  }>
) => {
  if (!supabase) {
    console.log('Mock: Saving contributed questions for user:', civicUser.id);
    const contributedQuestions = questions.map(q => ({
      id: `contrib_${Date.now()}_${Math.random()}`,
      contributor_id: civicUser.id,
      exam_type_id: examTypeId,
      state_code: stateCode,
      question_text: q.questionText,
      correct_answer: q.correctAnswer,
      explanation: q.explanation || '',
      difficulty: q.difficulty || 'medium',
      category: q.category || 'general',
      verified: civicUser.verificationLevel === 'professional',
      created_at: new Date().toISOString(),
    }));
    
    const existing = JSON.parse(localStorage.getItem(`contributed_${civicUser.id}`) || '[]');
    existing.push(...contributedQuestions);
    localStorage.setItem(`contributed_${civicUser.id}`, JSON.stringify(existing));
    return contributedQuestions;
  }

  try {
    // Ensure profile exists
    await createOrUpdateProfile(civicUser);
    
    const questionsToInsert = questions.map(q => ({
      contributor_id: civicUser.id,
      exam_type_id: examTypeId,
      state_code: stateCode,
      question_text: q.questionText,
      correct_answer: q.correctAnswer,
      explanation: q.explanation || '',
      difficulty: (q.difficulty || 'medium') as 'easy' | 'medium' | 'hard',
      category: q.category || 'general',
      verified: civicUser.verificationLevel === 'professional', // Auto-verify for professional level
    }));

    const { data, error } = await supabase
      .from('contributed_questions')
      .insert(questionsToInsert)
      .select();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Failed to save contributed questions:', error);
    throw error;
  }
};

export const getContributedQuestions = async (examTypeId: string, stateCode: string) => {
  if (!supabase) {
    console.log('Mock: Getting contributed questions for:', examTypeId, stateCode);
    return [];
  }

  try {
    const { data, error } = await supabase
      .from('contributed_questions')
      .select('*')
      .eq('exam_type_id', examTypeId)
      .eq('state_code', stateCode)
      .eq('verified', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Failed to get contributed questions:', error);
    return [];
  }
};

// Get user study sessions for progress tracking
export const getUserStudySessions = async (userId: string, registrationId?: string) => {
  if (!supabase) {
    console.log('Mock: Getting study sessions for user:', userId);
    const sessions = JSON.parse(localStorage.getItem(`sessions_${userId}`) || '[]');
    return registrationId ? sessions.filter((s: any) => s.registration_id === registrationId) : sessions;
  }

  try {
    let query = supabase
      .from('study_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (registrationId) {
      query = query.eq('registration_id', registrationId);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Failed to get study sessions:', error);
    return [];
  }
};

// Check if subscription is expired and needs renewal
export const checkSubscriptionExpiry = async (userId: string) => {
  if (!supabase) {
    const mockSubscription = localStorage.getItem(`subscription_${userId}`);
    if (mockSubscription) {
      const subscription = JSON.parse(mockSubscription);
      const now = new Date();
      const endDate = new Date(subscription.current_period_end);
      const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return {
        isExpired: now >= endDate,
        daysLeft: Math.max(0, daysLeft),
        expiryDate: endDate,
      };
    }
    return { isExpired: true, daysLeft: 0, expiryDate: new Date() };
  }

  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('current_period_end')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') {
      return { isExpired: true, daysLeft: 0, expiryDate: new Date() };
    }

    if (data) {
      const now = new Date();
      const endDate = new Date(data.current_period_end);
      const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      return {
        isExpired: now >= endDate,
        daysLeft: Math.max(0, daysLeft),
        expiryDate: endDate,
      };
    }

    return { isExpired: true, daysLeft: 0, expiryDate: new Date() };
  } catch (error) {
    console.error('Failed to check subscription expiry:', error);
    return { isExpired: true, daysLeft: 0, expiryDate: new Date() };
  }
};