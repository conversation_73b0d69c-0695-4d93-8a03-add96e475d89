import { useState, useEffect } from 'react';
import { civicAuth, CivicUser } from '../lib/civic';
import { 
  getUserExamRegistrations, 
  createExamRegistration,
  updateExamResult,
  saveStudySession,
  saveContributedQuestions,
  checkUserSubscription as checkSubscription,
  createSubscription
} from '../lib/supabase';
import { validateApiKey } from '../utils/api';

interface StudySection {
  id: string;
  title: string;
  content: string;
}

interface UserAnswer {
  questionId: string;
  userAnswer: string;
  isCorrect: boolean;
  timeSpent: number;
  timestamp: Date;
}

interface ExamRegistration {
  id: string;
  userId: string;
  examType: {
    id: string;
    name: string;
    description: string;
    category: string;
    timeLimit: number;
    passingScore: number;
    questionCount: number;
  };
  state: {
    code: string;
    name: string;
  };
  examDate: Date;
  paymentStatus: 'pending' | 'completed' | 'failed';
  stripePaymentId?: string;
  isActive: boolean;
  examResult?: 'pass' | 'fail';
  createdAt: Date;
  updatedAt: Date;
}

interface AppState {
  currentView: 'landing' | 'auth' | 'subscription' | 'registration' | 'upload' | 'study' | 'results';
  setCurrentView: (view: 'landing' | 'auth' | 'subscription' | 'registration' | 'upload' | 'study' | 'results') => void;
  currentUser: CivicUser | null;
  currentRegistration: ExamRegistration | null;
  studyContent: string;
  selectedDifficulty: 'easy' | 'medium' | 'hard';
  darkMode: boolean;
  apiKeyValid: boolean | null;
  hasActiveSubscription: boolean;
  loading: boolean;
  errorMessage: string | null;
  handleCivicAuth: (user: CivicUser) => Promise<void>;
  handleRegistrationComplete: (examData: {
    examTypeId: string;
    examTypeName: string;
    stateCode: string;
    stateName: string;
    examDate: string;
    studentInfo: any;
  }) => Promise<void>;
  handleContentReady: (content: string, sections: StudySection[]) => void;
  handleResultSubmitted: (result: 'pass' | 'fail', contributedQuestions?: string[]) => Promise<void>;
  handleStudySessionComplete: (answers: UserAnswer[], score: number, strategyType: 'flashcards' | 'multiple_choice' | 'typed_answer') => Promise<void>;
  handlePayment: () => Promise<void>;
}

export default function useAppState(): AppState {
  const [currentView, setCurrentView] = useState<'landing' | 'auth' | 'subscription' | 'registration' | 'upload' | 'study' | 'results'>('landing');
  const [currentUser, setCurrentUser] = useState<CivicUser | null>(null);
  const [currentRegistration, setCurrentRegistration] = useState<ExamRegistration | null>(null);
  const [studyContent, setStudyContent] = useState<string>('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');
  const [darkMode, setDarkMode] = useState(true);
  const [apiKeyValid, setApiKeyValid] = useState<boolean | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const checkApiKey = async () => {
      try {
        const isValid = await validateApiKey();
        setApiKeyValid(isValid);
      } catch (error) {
        console.error('App: API key validation failed:', error);
        setApiKeyValid(false);
      }
    };
    checkApiKey();
  }, []);

  useEffect(() => {
    const checkExistingSession = async () => {
      try {
        const user = civicAuth.getCurrentUser();
        console.log('App: Checking existing session, user:', user);
        if (user && user.id) {
          setCurrentUser(user);
          await checkUserSubscription(user);
        } else {
          console.warn('App: No valid user found in session');
          setCurrentView('landing');
        }
      } catch (error) {
        console.error('App: Failed to check existing session:', error);
        setErrorMessage('Failed to load user session. Please log in again.');
        setCurrentView('landing');
      } finally {
        setLoading(false);
      }
    };

    if (apiKeyValid !== null) {
      checkExistingSession();
    }
  }, [apiKeyValid]);

  // Handle Stripe payment success redirect
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const paymentIntent = urlParams.get('payment_intent');
    const paymentIntentClientSecret = urlParams.get('payment_intent_client_secret');
    
    if (paymentIntent && paymentIntentClientSecret) {
      console.log('App: Payment success detected, payment_intent:', paymentIntent);
      
      const verifyPayment = async (user: CivicUser | null, retries: number = 5, delay: number = 1000): Promise<void> => {
        if (!user || !user.id) {
          console.log('App: No currentUser for payment verification, retrying...');
          if (retries > 0) {
            const currentUser = civicAuth.getCurrentUser();
            if (currentUser && currentUser.id) {
              setCurrentUser(currentUser);
              await new Promise(resolve => setTimeout(resolve, delay));
              return verifyPayment(currentUser, retries - 1, delay * 1.5);
            }
            await new Promise(resolve => setTimeout(resolve, delay));
            return verifyPayment(null, retries - 1, delay * 1.5);
          }
          setErrorMessage('Payment successful but user session not found. Please log in to continue.');
          setCurrentView('auth');
          return;
        }
        
        try {
          console.log('App: Creating subscription for payment_intent:', paymentIntent, 'user:', user.id);
          const subscription = await createSubscription(user, paymentIntent);
          if (subscription) {
            console.log('App: Subscription created successfully:', subscription);
            setHasActiveSubscription(true);
            setCurrentView('registration');
            setErrorMessage(null);
            // Clean up URL
            window.history.replaceState({}, document.title, window.location.pathname);
          } else {
            throw new Error('Subscription creation returned no data');
          }
        } catch (error) {
          console.error('App: Payment verification failed:', error);
          setErrorMessage('Payment was successful but subscription activation failed. Please contact support.');
          setCurrentView('subscription');
        }
      };

      if (currentUser) {
        verifyPayment(currentUser);
      } else {
        // Wait for user to be loaded
        const checkUser = setInterval(() => {
          const user = civicAuth.getCurrentUser();
          if (user && user.id) {
            setCurrentUser(user);
            verifyPayment(user);
            clearInterval(checkUser);
          }
        }, 500);
        
        // Timeout after 10 seconds
        setTimeout(() => {
          clearInterval(checkUser);
          if (!currentUser) {
            setErrorMessage('Payment successful but user session expired. Please log in to activate your subscription.');
            setCurrentView('auth');
          }
        }, 10000);
      }
    }

    // Handle payment failure
    const paymentFailed = urlParams.get('payment_failed');
    if (paymentFailed) {
      setErrorMessage('Payment was not completed. Please try again.');
      setCurrentView('subscription');
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [currentUser]);

  const checkUserSubscription = async (user: CivicUser) => {
    if (!user || !user.id) {
      console.error('App: checkUserSubscription: User is undefined or missing id:', user);
      setErrorMessage('Invalid user data. Please log in again.');
      setCurrentView('auth');
      return;
    }

    try {
      console.log('App: Checking subscription for user_id:', user.id);
      const subscription = await checkSubscription(user.id);
      setHasActiveSubscription(!!subscription);
      
      if (subscription) {
        console.log('App: Active subscription found:', subscription);
        await loadUserData(user);
      } else {
        console.log('App: No active subscription found for user_id:', user.id);
        setCurrentView('subscription');
      }
    } catch (error) {
      console.error('App: Failed to check subscription:', error);
      setHasActiveSubscription(false);
      setErrorMessage('Failed to verify subscription status. Please try again.');
      setCurrentView('subscription');
    }
  };

  const loadUserData = async (user: CivicUser) => {
    if (!user || !user.id) {
      console.error('App: loadUserData: User is undefined or missing id:', user);
      setErrorMessage('Invalid user data. Please log in again.');
      setCurrentView('auth');
      return;
    }

    try {
      const registrations = await getUserExamRegistrations(user.id);
      if (registrations && registrations.length > 0) {
        const activeRegistration = registrations.find(r => r.is_active);
        if (activeRegistration) {
          setCurrentRegistration({
            id: activeRegistration.id,
            userId: activeRegistration.user_id,
            examType: {
              id: activeRegistration.exam_type_id,
              name: activeRegistration.exam_type_name,
              description: '',
              category: 'other',
              timeLimit: 180,
              passingScore: 70,
              questionCount: 100,
            },
            state: {
              code: activeRegistration.state_code,
              name: activeRegistration.state_name,
            },
            examDate: new Date(activeRegistration.exam_date),
            paymentStatus: 'completed',
            stripePaymentId: activeRegistration.stripe_payment_intent_id,
            isActive: activeRegistration.is_active,
            examResult: activeRegistration.exam_result === 'pending' ? undefined : activeRegistration.exam_result,
            createdAt: new Date(activeRegistration.created_at),
            updatedAt: new Date(activeRegistration.updated_at),
          });
          
          // Check if exam date has passed and no result submitted
          if (new Date() > new Date(activeRegistration.exam_date) && activeRegistration.exam_result === 'pending') {
            setCurrentView('results');
          } else {
            setCurrentView('upload');
          }
        } else {
          setCurrentView('registration');
        }
      } else {
        setCurrentView('registration');
      }
    } catch (error) {
      console.error('App: Failed to load user data:', error);
      setErrorMessage('Failed to load user data. Please try again.');
      setCurrentView('registration');
    }
  };

  const handleCivicAuth = async (user: CivicUser) => {
    if (!user || !user.id) {
      console.error('App: handleCivicAuth: Invalid user object:', user);
      setErrorMessage('Authentication failed. Please try logging in again.');
      setCurrentView('auth');
      return;
    }

    console.log('App: handleCivicAuth: Setting user:', user);
    setCurrentUser(user);
    setErrorMessage(null);
    await checkUserSubscription(user);
  };

  const handleRegistrationComplete = async (examData: {
    examTypeId: string;
    examTypeName: string;
    stateCode: string;
    stateName: string;
    examDate: string;
    studentInfo: any;
  }) => {
    if (!currentUser || !currentUser.id) {
      console.error('App: handleRegistrationComplete: No valid user');
      setErrorMessage('Please log in to register for an exam.');
      setCurrentView('auth');
      return;
    }

    try {
      const subscription = await checkSubscription(currentUser.id);
      if (!subscription) {
        throw new Error('No active subscription found');
      }

      const registration = await createExamRegistration(currentUser, subscription.id, examData);
      
      setCurrentRegistration({
        id: registration.id,
        userId: registration.user_id,
        examType: {
          id: registration.exam_type_id,
          name: registration.exam_type_name,
          description: '',
          category: 'other',
          timeLimit: 180,
          passingScore: 70,
          questionCount: 100,
        },
        state: {
          code: registration.state_code,
          name: registration.state_name,
        },
        examDate: new Date(registration.exam_date),
        paymentStatus: 'completed',
        stripePaymentId: registration.stripe_payment_intent_id,
        isActive: registration.is_active,
        createdAt: new Date(registration.created_at),
        updatedAt: new Date(registration.updated_at),
      });
      
      setCurrentView('upload');
      setErrorMessage(null);
    } catch (error) {
      console.error('App: Failed to create exam registration:', error);
      setErrorMessage('Failed to register for exam. Please try again.');
    }
  };

  const handleContentReady = (content: string, sections: StudySection[]) => {
    setStudyContent(content);
    setCurrentView('study');
    setErrorMessage(null);
  };

  const handleResultSubmitted = async (result: 'pass' | 'fail', contributedQuestions?: string[]) => {
    if (!currentRegistration || !currentUser || !currentUser.id) {
      console.error('App: handleResultSubmitted: No valid user or registration');
      setErrorMessage('Please log in to submit exam results.');
      setCurrentView('auth');
      return;
    }

    try {
      await updateExamResult(currentRegistration.id, result);
      
      if (result === 'pass' && contributedQuestions && contributedQuestions.length > 0) {
        const questions = contributedQuestions.map(q => ({
          questionText: q,
          correctAnswer: '',
          explanation: '',
          difficulty: 'medium' as const,
          category: 'general',
        }));
        
        await saveContributedQuestions(
          currentUser,
          currentRegistration.examType.id,
          currentRegistration.state.code,
          questions
        );
      }
      
      setCurrentRegistration(prev => prev ? {
        ...prev,
        examResult: result,
        updatedAt: new Date(),
      } : null);
      
      setCurrentView('upload');
      setErrorMessage(null);
    } catch (error) {
      console.error('App: Failed to submit exam result:', error);
      setErrorMessage('Failed to submit exam result. Please try again.');
    }
  };

  const handleStudySessionComplete = async (answers: UserAnswer[], score: number, strategyType: 'flashcards' | 'multiple_choice' | 'typed_answer') => {
    if (!currentUser || !currentUser.id || !currentRegistration) {
      console.error('App: handleStudySessionComplete: No valid user or registration');
      setErrorMessage('Please log in to save study session.');
      setCurrentView('auth');
      return;
    }

    try {
      const correctAnswers = answers.filter(a => a.isCorrect).length;
      const timeSpentSeconds = answers.reduce((total, answer) => total + answer.timeSpent, 0);
      
      await saveStudySession({
        civicUser: currentUser,
        registrationId: currentRegistration.id,
        strategyType,
        questionsAnswered: answers.length,
        correctAnswers,
        scorePercentage: score,
        timeSpentSeconds,
      });
      
      setErrorMessage(null);
    } catch (error) {
      console.error('App: Failed to save study session:', error);
      setErrorMessage('Failed to save study session. Please try again.');
    }
  };

  const handlePayment = async () => {
    if (!currentUser || !currentUser.id) {
      console.error('App: handlePayment: No valid user');
      setErrorMessage('Please log in to proceed with payment.');
      setCurrentView('auth');
      return;
    }

    try {
      // Updated Stripe checkout URL for $1.00 payment
      const checkoutUrl = `https://buy.stripe.com/test_00g4gw5WA6VW4v6000?client_reference_id=${currentUser.id}`;
      console.log('App: Initiating payment, redirecting to:', checkoutUrl);
      window.location.href = checkoutUrl;
    } catch (error) {
      console.error('App: Failed to initiate payment:', error);
      setErrorMessage('Failed to initiate payment. Please try again.');
      setCurrentView('subscription');
    }
  };

  return {
    currentView,
    setCurrentView,
    currentUser,
    currentRegistration,
    studyContent,
    selectedDifficulty,
    darkMode,
    apiKeyValid,
    hasActiveSubscription,
    loading,
    errorMessage,
    handleCivicAuth,
    handleRegistrationComplete,
    handleContentReady,
    handleResultSubmitted,
    handleStudySessionComplete,
    handlePayment,
  };
}