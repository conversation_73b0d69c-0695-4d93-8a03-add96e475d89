{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@civic/auth": "^0.9.5", "@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@heroicons/react": "^2.2.0", "@stripe/react-stripe-js": "^3.8.1", "@stripe/stripe-js": "^7.7.0", "@supabase/supabase-js": "^2.53.0", "framer-motion": "^12.23.5", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^24.1.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}