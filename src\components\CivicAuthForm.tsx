import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Shield, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { civicAuth, CivicUser } from '../lib/civic';

interface CivicAuthFormProps {
  onAuthSuccess: (user: CivicUser) => void;
}

const CivicAuthForm: React.FC<CivicAuthFormProps> = ({ onAuthSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [verificationStep, setVerificationStep] = useState<'idle' | 'connecting' | 'verifying' | 'complete'>('idle');

  // Check for existing session on mount
  useEffect(() => {
    const existingUser = civicAuth.getCurrentUser();
    console.log('CivicAuthForm: Checking existing user on mount:', existingUser);
    if (existingUser && existingUser.id) {
      onAuthSuccess(existingUser);
    } else {
      console.warn('CivicAuthForm: No valid user found in session');
    }
  }, [onAuthSuccess]);

  const handleCivicAuth = async () => {
    setLoading(true);
    setError(null);
    setVerificationStep('connecting');

    try {
      // Replace with real Civic authentication if not using mock
      const user: CivicUser = await civicAuth.login(); // Assuming civicAuth.login() returns a Promise<CivicUser>
      console.log('CivicAuthForm: Authenticated user:', user);

      if (!user || !user.id) {
        throw new Error('Invalid user data from Civic authentication');
      }

      civicAuth.setUser(user);
      setVerificationStep('complete');
      onAuthSuccess(user);
    } catch (err: any) {
      console.error('CivicAuthForm: Authentication error:', err);
      setError(err.message || 'Authentication failed. Please try again.');
      setVerificationStep('idle');
    } finally {
      setLoading(false);
    }
  };

  const getStepContent = () => {
    switch (verificationStep) {
      case 'connecting':
        return {
          icon: <Loader2 className="w-12 h-12 animate-spin text-blue-400" />,
          title: 'Connecting to Civic',
          description: 'Opening secure identity verification...'
        };
      case 'verifying':
        return {
          icon: <Shield className="w-12 h-12 text-purple-400 animate-pulse" />,
          title: 'Verifying Identity',
          description: 'Please complete the verification process in the Civic modal...'
        };
      case 'complete':
        return {
          icon: <CheckCircle className="w-12 h-12 text-green-400" />,
          title: 'Verification Complete',
          description: 'Identity verified successfully! Redirecting...'
        };
      default:
        return {
          icon: <Shield className="w-12 h-12 text-purple-400" />,
          title: 'Secure Identity Verification',
          description: 'Verify your identity with Civic to access professional exam preparation'
        };
    }
  };

  const stepContent = getStepContent();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-md mx-auto"
    >
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700 text-center">
        <div className="mb-6">
          {stepContent.icon}
        </div>

        <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
          {stepContent.title}
        </h2>
        
        <p className="text-gray-400 mb-8">
          {stepContent.description}
        </p>

        {verificationStep === 'idle' && (
          <>
            <div className="mb-6 space-y-3 text-sm text-gray-400">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span>Blockchain-based identity verification</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span>Professional licensing compliance</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span>Secure and privacy-focused</span>
              </div>
            </div>

            <button
              onClick={handleCivicAuth}
              disabled={loading}
              className="w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:scale-105 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {loading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <span>Connecting...</span>
                </>
              ) : (
                <>
                  <Shield className="w-5 h-5" />
                  <span>Verify with Civic</span>
                </>
              )}
            </button>
          </>
        )}

        {error && (
          <div className="mt-4 p-3 bg-red-900/50 border border-red-500 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-red-400" />
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          </div>
        )}

        {verificationStep !== 'idle' && (
          <div className="mt-6 p-4 bg-blue-600/20 border border-blue-600 rounded-lg">
            <p className="text-blue-200 text-sm">
              This is a demo implementation. In production, this would open the real Civic verification modal.
            </p>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default CivicAuthForm;