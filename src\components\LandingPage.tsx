import React from 'react';
import { motion } from 'framer-motion';
import { 
  GraduationCap, 
  Brain, 
  Trophy, 
  Users, 
  Star,
  Play,
  Globe,
  Facebook,
  Linkedin,
  Instagram,
  Shield,
  MessageCircle
} from 'lucide-react';

// Custom X (Twitter) icon component
const XIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

// Custom icons for new social platforms
const BlueskyIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 10.8c-1.087-2.114-4.046-6.053-6.798-7.995C2.566.944 1.561 1.266.902 2.104.139 3.097.139 5.863.139 5.863s.278 1.518.835 2.268c.557.75 1.668.972 2.224.972.556 0 .556-.222.556-.222s0-.306.278-.528c.278-.222.556-.222.556-.222s.556.306.556.528c0 .222-.278.528-.278.528s-.278.306-.556.306c-.278 0-.556-.084-.556-.084s-.278.222-.278.528c0 .306.278.528.278.528s.278.222.556.222c.278 0 .556-.222.556-.222s.278-.306.278-.528c0-.222-.278-.528-.278-.528s-.278-.306-.556-.306c-.278 0-.556.084-.556.084s-.278-.222-.278-.528c0-.306.278-.528.278-.528s.278-.222.556-.222c.278 0 .556.222.556.222s.278.306.278.528c0 .222-.278.528-.278.528s-.278.306-.556.306"/>
  </svg>
);

const RedditIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z"/>
  </svg>
);

const SubstackIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M22.539 8.242H1.46V5.406h21.08v2.836zM1.46 10.812V24L12 18.11 22.54 24V10.812H1.46zM22.54 0H1.46v2.836h21.08V0z"/>
  </svg>
);

const ThreadsIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12.186 24h-.007c-3.581-.024-6.334-1.205-8.184-3.509C2.35 18.44 1.5 15.586 1.5 12.068c0-3.518.85-6.373 2.495-8.424C5.845 1.205 8.598.024 12.186 0h.007c3.588.024 6.341 1.205 8.191 3.509C22.15 5.559 23 8.413 23 11.932c0 3.518-.85 6.373-2.495 8.424C18.859 22.795 16.106 23.976 12.186 24zM12 2.25c-2.69 0-4.923.918-6.64 2.73C3.643 6.793 2.75 9.15 2.75 12.068c0 2.918.893 5.275 2.61 7.088C6.077 21.082 8.31 22 12 22s5.923-.918 7.64-2.73c1.717-1.813 2.61-4.17 2.61-7.088 0-2.918-.893-5.275-2.61-7.088C17.923 3.168 15.69 2.25 12 2.25z"/>
  </svg>
);

const TikTokIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
  </svg>
);

interface LandingPageProps {
  onGetStarted: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
  const socialLinks = [
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: XIcon, href: '#', label: 'X (Twitter)' },
    { icon: Linkedin, href: '#', label: 'LinkedIn' },
    { icon: Instagram, href: '#', label: 'Instagram' },
    { icon: BlueskyIcon, href: '#', label: 'Bluesky' },
    { icon: RedditIcon, href: '#', label: 'Reddit' },
    { icon: SubstackIcon, href: '#', label: 'Substack' },
    { icon: ThreadsIcon, href: '#', label: 'Threads' },
    { icon: TikTokIcon, href: '#', label: 'TikTok' },
  ];

  const languages = [
    'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 
    'Chinese', 'Japanese', 'Korean', 'Arabic', 'Russian', 'Hindi'
  ];

  return (
    <div className="min-h-screen">
      {/* Google Translate Widget at Top */}
      <div className="bg-gray-800/50 border-b border-gray-700 py-2">
        <div className="max-w-6xl mx-auto px-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Globe className="w-5 h-5 text-purple-400" />
            <select className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500">
              {languages.map((lang, index) => (
                <option key={index} value={lang.toLowerCase()}>
                  {lang}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-400 text-sm">Share:</span>
            <div className="flex space-x-2">
              {socialLinks.slice(0, 5).map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="p-1 text-gray-400 hover:text-purple-400 transition-colors"
                  aria-label={social.label}
                >
                  <social.icon className="w-4 h-4" />
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                Master Your
              </span>
              <br />
              <span className="text-white">Professional Exam</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              AI-powered study platform designed specifically for professional licensing exams. 
              Get personalized practice questions from students who passed in your state.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <button
                onClick={onGetStarted}
                className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg text-lg font-semibold hover:scale-105 transition-all shadow-lg flex items-center space-x-2"
              >
                <Shield className="w-5 h-5" />
                <span>Secure Identity Verification</span>
              </button>
              
              <button className="flex items-center space-x-2 px-6 py-4 border border-gray-600 text-gray-300 rounded-lg hover:border-purple-500 hover:text-white transition-all">
                <Play className="w-5 h-5" />
                <span>Watch Demo</span>
              </button>
            </div>

            {/* Video Placeholder */}
            <div className="max-w-4xl mx-auto mb-16">
              <div className="relative bg-gray-800/50 rounded-xl border border-gray-700 aspect-video flex items-center justify-center">
                <div className="text-center">
                  <Play className="w-16 h-16 mx-auto mb-4 text-purple-400" />
                  <h3 className="text-xl font-semibold text-white mb-2">Platform Demo Video</h3>
                  <p className="text-gray-400">See how our AI-powered study system works</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Why Choose Our Platform?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Built specifically for professional licensing exams with real questions from successful candidates
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: Brain,
                title: 'AI-Powered Learning',
                description: 'Personalized study strategies that adapt to your learning style and progress'
              },
              {
                icon: Users,
                title: 'Real Exam Questions',
                description: 'Questions contributed by students who passed their exams in your specific state'
              },
              {
                icon: Trophy,
                title: 'Proven Results',
                description: 'Higher pass rates with our targeted practice and feedback system'
              },
              {
                icon: GraduationCap,
                title: 'Professional Focus',
                description: 'Specialized for medical, legal, engineering, and other professional licensing exams'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 text-center"
              >
                <feature.icon className="w-12 h-12 mx-auto mb-4 text-purple-400" />
                <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                <p className="text-gray-400">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Study Strategies Section */}
      <section className="py-20 px-4 bg-gray-900/50">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-6 text-white">
              Three Powerful Study Methods
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Choose the learning approach that works best for your exam preparation
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: 'Flash Cards',
                description: '5-second question display followed by 5-second answer reveals',
                features: ['Timed memory reinforcement', 'Spaced repetition', 'Progress tracking']
              },
              {
                title: 'Multiple Choice',
                description: 'Realistic exam-style questions with 5 answer options and instant feedback',
                features: ['5 answer options', 'Immediate results', 'Performance analytics']
              },
              {
                title: 'Type Text Answers',
                description: 'Write detailed responses under actual exam time constraints',
                features: ['Real exam timing', 'Deep understanding', 'Comprehensive feedback']
              }
            ].map((method, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700"
              >
                <h3 className="text-xl font-semibold text-white mb-3">{method.title}</h3>
                <p className="text-gray-400 mb-4">{method.description}</p>
                <ul className="space-y-2">
                  {method.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-300">
                      <Star className="w-4 h-4 text-purple-400 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 border-t border-gray-800">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            {/* Social Media */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Follow Us</h3>
              <div className="grid grid-cols-3 gap-2">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    className="p-2 bg-gray-800 rounded-lg hover:bg-purple-600 transition-colors text-center"
                    aria-label={social.label}
                  >
                    <social.icon className="w-5 h-5 text-gray-300 mx-auto" />
                  </a>
                ))}
              </div>
            </div>

            {/* Language Selection */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Globe className="w-5 h-5 mr-2" />
                Language
              </h3>
              <select className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 w-full">
                {languages.map((lang, index) => (
                  <option key={index} value={lang.toLowerCase()}>
                    {lang}
                  </option>
                ))}
              </select>
            </div>

            {/* Contact */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Contact</h3>
              <div className="space-y-2 text-gray-400 text-sm">
                <p>Questions about our platform?</p>
                <p>Email: <EMAIL></p>
                <p>Phone: **************</p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 text-center">
            <p className="text-gray-400 text-sm">
              © 2025 Professional Exam Prep Platform. All rights reserved.
            </p>
            <p className="text-gray-500 text-xs mt-2">
              Secure payments powered by Stripe • Revenue sharing: 50/50 after operational costs
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;