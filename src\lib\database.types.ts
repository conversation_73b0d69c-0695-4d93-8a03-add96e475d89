export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          wallet_address: string | null
          verification_level: 'basic' | 'kyc' | 'professional'
          is_verified: boolean
          verified_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          wallet_address?: string | null
          verification_level?: 'basic' | 'kyc' | 'professional'
          is_verified?: boolean
          verified_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          wallet_address?: string | null
          verification_level?: 'basic' | 'kyc' | 'professional'
          is_verified?: boolean
          verified_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          stripe_payment_intent_id: string | null
          status: 'active' | 'inactive' | 'past_due' | 'canceled'
          amount_paid: number
          current_period_start: string
          current_period_end: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          stripe_payment_intent_id?: string | null
          status?: 'active' | 'inactive' | 'past_due' | 'canceled'
          amount_paid?: number
          current_period_start?: string
          current_period_end?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          stripe_payment_intent_id?: string | null
          status?: 'active' | 'inactive' | 'past_due' | 'canceled'
          amount_paid?: number
          current_period_start?: string
          current_period_end?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      exam_registrations: {
        Row: {
          id: string
          user_id: string
          subscription_id: string
          exam_type_id: string
          exam_type_name: string
          state_code: string
          state_name: string
          exam_date: string
          exam_result: 'pending' | 'pass' | 'fail'
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          subscription_id: string
          exam_type_id: string
          exam_type_name: string
          state_code: string
          state_name: string
          exam_date: string
          exam_result?: 'pending' | 'pass' | 'fail'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          subscription_id?: string
          exam_type_id?: string
          exam_type_name?: string
          state_code?: string
          state_name?: string
          exam_date?: string
          exam_result?: 'pending' | 'pass' | 'fail'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "exam_registrations_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "exam_registrations_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          }
        ]
      }
      study_sessions: {
        Row: {
          id: string
          user_id: string
          registration_id: string
          strategy_type: 'flashcards' | 'multiple_choice' | 'typed_answer'
          questions_answered: number
          correct_answers: number
          score_percentage: number
          time_spent_seconds: number
          completed_at: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          registration_id: string
          strategy_type: 'flashcards' | 'multiple_choice' | 'typed_answer'
          questions_answered?: number
          correct_answers?: number
          score_percentage?: number
          time_spent_seconds?: number
          completed_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          registration_id?: string
          strategy_type?: 'flashcards' | 'multiple_choice' | 'typed_answer'
          questions_answered?: number
          correct_answers?: number
          score_percentage?: number
          time_spent_seconds?: number
          completed_at?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_sessions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_sessions_registration_id_fkey"
            columns: ["registration_id"]
            referencedRelation: "exam_registrations"
            referencedColumns: ["id"]
          }
        ]
      }
      contributed_questions: {
        Row: {
          id: string
          contributor_id: string
          exam_type_id: string
          state_code: string
          question_text: string
          correct_answer: string
          explanation: string
          difficulty: 'easy' | 'medium' | 'hard'
          category: string
          verified: boolean
          created_at: string
        }
        Insert: {
          id?: string
          contributor_id: string
          exam_type_id: string
          state_code: string
          question_text: string
          correct_answer: string
          explanation?: string
          difficulty?: 'easy' | 'medium' | 'hard'
          category?: string
          verified?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          contributor_id?: string
          exam_type_id?: string
          state_code?: string
          question_text?: string
          correct_answer?: string
          explanation?: string
          difficulty?: 'easy' | 'medium' | 'hard'
          category?: string
          verified?: boolean
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contributed_questions_contributor_id_fkey"
            columns: ["contributor_id"]
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_subscription_access: {
        Args: {
          user_uuid: string
        }
        Returns: boolean
      }
      update_subscription_status: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      subscription_status: 'active' | 'inactive' | 'past_due' | 'canceled'
      exam_result_status: 'pending' | 'pass' | 'fail'
      study_strategy_type: 'flashcards' | 'multiple_choice' | 'typed_answer'
      difficulty_level: 'easy' | 'medium' | 'hard'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}