import React from 'react';
import { AnimatePresence } from 'framer-motion';
import StarBackground from './components/StarBackground';
import useAppState from './lib/useAppState';
import LandingPage from './components/LandingPage';
import CivicAuthForm from './components/CivicAuthForm';
import SubscriptionGate from './components/SubscriptionGate';
import ExamRegistration from './components/ExamRegistration';
import StudyMaterialUpload from './components/StudyMaterialUpload';
import StudyStrategies from './components/StudyStrategies';
import ExamResultSubmission from './components/ExamResultSubmission';

function App() {
  const {
    currentView,
    setCurrentView,
    currentUser,
    currentRegistration,
    studyContent,
    selectedDifficulty,
    darkMode,
    apiKeyValid,
    hasActiveSubscription,
    loading,
    errorMessage,
    handleCivicAuth,
    handleRegistrationComplete,
    handleContentReady,
    handleResultSubmitted,
    handleStudySessionComplete,
    handlePayment,
  } = useAppState();

  if (loading || apiKeyValid === null) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <StarBackground darkMode={darkMode} />
        <div className="text-center relative z-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  if (apiKeyValid === false) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <StarBackground darkMode={darkMode} />
        <div className="text-center relative z-10">
          <div className="bg-red-900/50 border border-red-500 rounded-xl p-8 max-w-md mx-auto">
            <h2 className="text-2xl font-bold text-red-300 mb-4">Configuration Error</h2>
            <p className="text-red-200 mb-4">Groq API key is not configured or invalid.</p>
            <p className="text-sm text-red-300">Add your Groq API key to the .env file as VITE_GROQ_API_KEY</p>
          </div>
        </div>
      </div>
    );
  }

  const renderContent = () => {
    switch (currentView) {
      case 'landing': return <LandingPage onGetStarted={() => setCurrentView('auth')} />;
      case 'auth': return (
        <div>
          {errorMessage && <div className="bg-red-900/50 border border-red-500 rounded-xl p-4 mb-4 max-w-md mx-auto text-red-200">{errorMessage}</div>}
          <CivicAuthForm onAuthSuccess={handleCivicAuth} />
        </div>
      );
      case 'subscription': return (
        <div>
          {errorMessage && <div className="bg-red-900/50 border border-red-500 rounded-xl p-4 mb-4 max-w-md mx-auto text-red-200">{errorMessage}</div>}
          <SubscriptionGate hasActiveSubscription={hasActiveSubscription} onPayment={handlePayment} />
        </div>
      );
      case 'registration': return hasActiveSubscription ? (
        <ExamRegistration onRegistrationComplete={handleRegistrationComplete} />
      ) : (
        <div>
          {errorMessage && <div className="bg-red-900/50 border border-red-500 rounded-xl p-4 mb-4 max-w-md mx-auto text-red-200">{errorMessage}</div>}
          <SubscriptionGate hasActiveSubscription={hasActiveSubscription} onPayment={handlePayment} />
        </div>
      );
      case 'upload': return hasActiveSubscription ? (
        <StudyMaterialUpload onContentReady={handleContentReady} />
      ) : (
        <div>
          {errorMessage && <div className="bg-red-900/50 border border-red-500 rounded-xl p-4 mb-4 max-w-md mx-auto text-red-200">{errorMessage}</div>}
          <SubscriptionGate hasActiveSubscription={hasActiveSubscription} onPayment={handlePayment} />
        </div>
      );
      case 'study': return hasActiveSubscription && currentRegistration ? (
        new Date() > currentRegistration.examDate && !currentRegistration.examResult ? (
          <div className="text-center py-12">
            <div className="bg-orange-900/50 border border-orange-500 rounded-xl p-8 max-w-md mx-auto">
              <h2 className="text-2xl font-bold text-orange-300 mb-4">Exam Date Passed</h2>
              <p className="text-orange-200 mb-6">Please report your exam results to continue using study features.</p>
              <button onClick={() => setCurrentView('results')} className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700">Report Results</button>
            </div>
          </div>
        ) : (
          <StudyStrategies examType={currentRegistration.examType.id} state={currentRegistration.state.code} studyContent={studyContent} difficulty={selectedDifficulty} onSessionComplete={handleStudySessionComplete} />
        )
      ) : (
        <div className="text-center py-12">
          <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-8 max-w-md mx-auto">
            <h2 className="text-2xl font-bold text-white mb-4">No Active Registration</h2>
            <p className="text-gray-300 mb-6">Please complete your exam registration to access study features.</p>
            <button onClick={() => setCurrentView('registration')} className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700">Register for Exam</button>
          </div>
        </div>
      );
      case 'results': return currentRegistration ? <ExamResultSubmission registrationId={currentRegistration.id} onResultSubmitted={handleResultSubmitted} /> : null;
      default: return <LandingPage onGetStarted={() => setCurrentView('auth')} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <StarBackground darkMode={darkMode} />
      <div className="relative z-10">
        <main className="container mx-auto px-4 py-8">
          <AnimatePresence mode="wait">{renderContent()}</AnimatePresence>
        </main>
      </div>
    </div>
  );
}

export default App;