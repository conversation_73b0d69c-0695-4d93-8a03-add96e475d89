/*
  # Professional Exam Platform Database Schema

  1. New Tables
    - `profiles` - User profile information linked to Supabase auth
      - `id` (uuid, references auth.users)
      - `email` (text)
      - `full_name` (text)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `subscriptions` - User subscription and payment tracking
      - `id` (uuid, primary key)
      - `user_id` (uuid, references profiles)
      - `stripe_customer_id` (text)
      - `stripe_subscription_id` (text)
      - `status` (subscription_status enum)
      - `current_period_start` (timestamp)
      - `current_period_end` (timestamp)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `exam_registrations` - User exam registrations
      - `id` (uuid, primary key)
      - `user_id` (uuid, references profiles)
      - `subscription_id` (uuid, references subscriptions)
      - `exam_type_id` (text)
      - `exam_type_name` (text)
      - `state_code` (text)
      - `state_name` (text)
      - `exam_date` (date)
      - `exam_result` (exam_result_status enum)
      - `is_active` (boolean)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `study_sessions` - Track user study progress
      - `id` (uuid, primary key)
      - `user_id` (uuid, references profiles)
      - `registration_id` (uuid, references exam_registrations)
      - `strategy_type` (study_strategy_type enum)
      - `questions_answered` (integer)
      - `correct_answers` (integer)
      - `score_percentage` (integer)
      - `time_spent_seconds` (integer)
      - `completed_at` (timestamp)
      - `created_at` (timestamp)
    
    - `contributed_questions` - Questions from users who passed
      - `id` (uuid, primary key)
      - `contributor_id` (uuid, references profiles)
      - `exam_type_id` (text)
      - `state_code` (text)
      - `question_text` (text)
      - `correct_answer` (text)
      - `explanation` (text)
      - `difficulty` (difficulty_level enum)
      - `category` (text)
      - `verified` (boolean)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to access their own data
    - Add policies for reading verified contributed questions
    - Add admin policies for managing content

  3. Enums
    - subscription_status: active, inactive, past_due, canceled
    - exam_result_status: pending, pass, fail
    - study_strategy_type: flashcards, multiple_choice, typed_answer
    - difficulty_level: easy, medium, hard
*/

-- Create custom types
CREATE TYPE subscription_status AS ENUM ('active', 'inactive', 'past_due', 'canceled');
CREATE TYPE exam_result_status AS ENUM ('pending', 'pass', 'fail');
CREATE TYPE study_strategy_type AS ENUM ('flashcards', 'multiple_choice', 'typed_answer');
CREATE TYPE difficulty_level AS ENUM ('easy', 'medium', 'hard');

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  full_name text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  stripe_customer_id text,
  stripe_subscription_id text,
  stripe_payment_intent_id text,
  status subscription_status DEFAULT 'inactive',
  amount_paid integer DEFAULT 15000, -- $150.00 in cents
  current_period_start timestamptz DEFAULT now(),
  current_period_end timestamptz DEFAULT (now() + interval '1 month'),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create exam_registrations table
CREATE TABLE IF NOT EXISTS exam_registrations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  subscription_id uuid NOT NULL REFERENCES subscriptions(id) ON DELETE CASCADE,
  exam_type_id text NOT NULL,
  exam_type_name text NOT NULL,
  state_code text NOT NULL,
  state_name text NOT NULL,
  exam_date date NOT NULL,
  exam_result exam_result_status DEFAULT 'pending',
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create study_sessions table
CREATE TABLE IF NOT EXISTS study_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  registration_id uuid NOT NULL REFERENCES exam_registrations(id) ON DELETE CASCADE,
  strategy_type study_strategy_type NOT NULL,
  questions_answered integer DEFAULT 0,
  correct_answers integer DEFAULT 0,
  score_percentage integer DEFAULT 0,
  time_spent_seconds integer DEFAULT 0,
  completed_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Create contributed_questions table
CREATE TABLE IF NOT EXISTS contributed_questions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  contributor_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  exam_type_id text NOT NULL,
  state_code text NOT NULL,
  question_text text NOT NULL,
  correct_answer text NOT NULL,
  explanation text DEFAULT '',
  difficulty difficulty_level DEFAULT 'medium',
  category text DEFAULT 'general',
  verified boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE exam_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE contributed_questions ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can read own profile"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile"
  ON profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Subscriptions policies
CREATE POLICY "Users can read own subscriptions"
  ON subscriptions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert own subscriptions"
  ON subscriptions
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own subscriptions"
  ON subscriptions
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

-- Exam registrations policies
CREATE POLICY "Users can read own exam registrations"
  ON exam_registrations
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert own exam registrations"
  ON exam_registrations
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own exam registrations"
  ON exam_registrations
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

-- Study sessions policies
CREATE POLICY "Users can read own study sessions"
  ON study_sessions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert own study sessions"
  ON study_sessions
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Contributed questions policies
CREATE POLICY "Users can read verified questions"
  ON contributed_questions
  FOR SELECT
  TO authenticated
  USING (verified = true);

CREATE POLICY "Users can insert own contributed questions"
  ON contributed_questions
  FOR INSERT
  TO authenticated
  WITH CHECK (contributor_id = auth.uid());

CREATE POLICY "Users can read own contributed questions"
  ON contributed_questions
  FOR SELECT
  TO authenticated
  USING (contributor_id = auth.uid());

-- Functions for automatic profile creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for automatic profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to check subscription status
CREATE OR REPLACE FUNCTION check_subscription_access(user_uuid uuid)
RETURNS boolean AS $$
DECLARE
  subscription_record subscriptions%ROWTYPE;
BEGIN
  SELECT * INTO subscription_record
  FROM subscriptions
  WHERE user_id = user_uuid
    AND status = 'active'
    AND current_period_end > now()
  ORDER BY created_at DESC
  LIMIT 1;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update subscription status
CREATE OR REPLACE FUNCTION update_subscription_status()
RETURNS void AS $$
BEGIN
  UPDATE subscriptions
  SET status = 'inactive'
  WHERE status = 'active'
    AND current_period_end < now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_exam_registrations_user_id ON exam_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_user_id ON study_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_contributed_questions_exam_state ON contributed_questions(exam_type_id, state_code);
CREATE INDEX IF NOT EXISTS idx_contributed_questions_verified ON contributed_questions(verified);