import React from 'react';
import { motion } from 'framer-motion';
import { CreditCard, Clock, CheckCircle, AlertTriangle } from 'lucide-react';

interface SubscriptionGateProps {
  hasActiveSubscription: boolean;
  subscriptionExpiry?: string;
  onPayment: () => void;
}

const SubscriptionGate: React.FC<SubscriptionGateProps> = ({ 
  hasActiveSubscription, 
  subscriptionExpiry,
  onPayment 
}) => {
  const isExpiringSoon = subscriptionExpiry && 
    new Date(subscriptionExpiry).getTime() - Date.now() < 7 * 24 * 60 * 60 * 1000; // 7 days

  if (hasActiveSubscription && !isExpiringSoon) {
    return null; // User has active subscription, no gate needed
  }

  if (isExpiringSoon) {
    return (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <div className="bg-orange-900/50 border border-orange-500 rounded-xl p-6">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-6 h-6 text-orange-400" />
            <div>
              <h3 className="font-semibold text-orange-300">Subscription Expiring Soon</h3>
              <p className="text-orange-200 text-sm">
                Your subscription expires on {new Date(subscriptionExpiry!).toLocaleDateString()}
              </p>
            </div>
          </div>
          <button
            onClick={onPayment}
            className="mt-4 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 text-sm"
          >
            Renew Subscription
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-2xl mx-auto text-center"
    >
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-12 border border-gray-700">
        <CreditCard className="w-16 h-16 mx-auto mb-6 text-purple-400" />
        
        <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
          Subscription Required
        </h2>
        
        <p className="text-xl text-gray-300 mb-8">
          Access our AI-powered exam preparation platform with personalized study strategies
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-gray-700/50 rounded-lg p-4">
            <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-400" />
            <h4 className="font-semibold text-white mb-1">AI Study Coach</h4>
            <p className="text-sm text-gray-400">Personalized learning strategies</p>
          </div>
          
          <div className="bg-gray-700/50 rounded-lg p-4">
            <Clock className="w-8 h-8 mx-auto mb-2 text-blue-400" />
            <h4 className="font-semibold text-white mb-1">30-Day Access</h4>
            <p className="text-sm text-gray-400">Full platform access for one month</p>
          </div>
          
          <div className="bg-gray-700/50 rounded-lg p-4">
            <CheckCircle className="w-8 h-8 mx-auto mb-2 text-purple-400" />
            <h4 className="font-semibold text-white mb-1">Real Exam Questions</h4>
            <p className="text-sm text-gray-400">From students who passed</p>
          </div>
        </div>

        <button
          onClick={onPayment}
          className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg text-lg font-semibold hover:scale-105 transition-all shadow-lg"
        >
          Get Started - Pay $1
        </button>

        <p className="text-sm text-gray-500 mt-4">
          Secure payment processing powered by Stripe
        </p>
      </div>
    </motion.div>
  );
};

export default SubscriptionGate;