import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Calendar, CreditCard, MapPin, GraduationCap, ArrowRight, Check, User, Home, Globe } from 'lucide-react';
import { ProfessionalExamType, USState } from '../types';
import { PROFESSIONAL_EXAMS, US_STATES } from '../data/examTypes';

interface ExamRegistrationProps {
  onRegistrationComplete: (examData: {
    examTypeId: string;
    examTypeName: string;
    stateCode: string;
    stateName: string;
    examDate: string;
    studentInfo: {
      name: string;
      address: string;
      city: string;
      state: string;
      country: string;
      zipCode: string;
      college: string;
    };
  }) => void;
}

const ExamRegistration: React.FC<ExamRegistrationProps> = ({ onRegistrationComplete }) => {
  const [step, setStep] = useState(1);
  const [selectedExam, setSelectedExam] = useState<ProfessionalExamType | null>(null);
  const [selectedState, setSelectedState] = useState<USState | null>(null);
  const [examDate, setExamDate] = useState('');
  const [studentInfo, setStudentInfo] = useState({
    name: '',
    address: '',
    city: '',
    state: '',
    country: 'United States',
    zipCode: '',
    college: '',
  });
  const [error, setError] = useState<string | null>(null);

  const countries = [
    'United States', 'Canada', 'United Kingdom', 'Australia', 'Germany', 
    'France', 'Spain', 'Italy', 'Netherlands', 'Sweden', 'Norway', 'Other'
  ];

  const handleExamSelect = (exam: ProfessionalExamType) => {
    setSelectedExam(exam);
    setStep(2);
  };

  const handleStateSelect = (state: USState) => {
    setSelectedState(state);
    setStep(3);
  };

  const handleStudentInfoSubmit = () => {
    if (studentInfo.name && studentInfo.address && studentInfo.city && studentInfo.state && studentInfo.zipCode) {
      setStep(4);
    } else {
      setError('Please fill in all required fields');
    }
  };

  const handleDateSelect = () => {
    if (examDate && selectedExam && selectedState) {
      onRegistrationComplete({
        examTypeId: selectedExam.id,
        examTypeName: selectedExam.name,
        stateCode: selectedState.code,
        stateName: selectedState.name,
        examDate,
        studentInfo,
      });
    }
  };

  const groupedExams = PROFESSIONAL_EXAMS.reduce((acc, exam) => {
    if (!acc[exam.category]) acc[exam.category] = [];
    acc[exam.category].push(exam);
    return acc;
  }, {} as Record<string, ProfessionalExamType[]>);

  const categoryNames = {
    medical: 'Medical & Healthcare',
    legal: 'Legal',
    engineering: 'Engineering',
    accounting: 'Accounting & Finance',
    'real-estate': 'Real Estate',
    nursing: 'Nursing',
    pharmacy: 'Pharmacy',
    architecture: 'Architecture',
    education: 'Education',
    finance: 'Financial Services',
    other: 'Other',
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
          Professional Exam Registration
        </h2>
        <p className="text-gray-500 text-lg">Complete your exam registration and student information</p>
      </div>

      <div className="flex justify-center mb-8">
        <div className="flex items-center space-x-4">
          {[1, 2, 3, 4].map((stepNum) => (
            <div key={stepNum} className="flex items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center font-medium ${
                  step >= stepNum ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-400'
                }`}
              >
                {step > stepNum ? <Check className="w-5 h-5" /> : stepNum}
              </div>
              {stepNum < 4 && (
                <ArrowRight
                  className={`w-5 h-5 mx-2 ${step > stepNum ? 'text-purple-400' : 'text-gray-600'}`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700">
        {step === 1 && (
          <div>
            <h3 className="text-xl font-bold mb-6 flex items-center">
              <GraduationCap className="w-6 h-6 mr-3 text-purple-400" />
              Select Your Professional Exam
            </h3>
            <div className="space-y-6">
              {Object.entries(groupedExams).map(([category, exams]) => (
                <div key={category}>
                  <h4 className="text-lg font-semibold text-gray-300 mb-3">
                    {categoryNames[category as keyof typeof categoryNames]}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {exams.map((exam) => (
                      <button
                        key={exam.id}
                        onClick={() => handleExamSelect(exam)}
                        className="p-4 bg-gray-700/50 rounded-lg border border-gray-600 hover:border-purple-500 hover:bg-purple-500/10 transition-all text-left"
                      >
                        <h5 className="font-medium text-white mb-1">{exam.name}</h5>
                        <p className="text-sm text-gray-400 mb-2">{exam.description}</p>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>{exam.questionCount} questions</span>
                          <span>{Math.floor(exam.timeLimit / 60)}h {exam.timeLimit % 60}m</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {step === 2 && selectedExam && (
          <div>
            <h3 className="text-xl font-bold mb-6 flex items-center">
              <MapPin className="w-6 h-6 mr-3 text-purple-400" />
              Select Your State
            </h3>
            <div className="mb-4 p-4 bg-purple-600/20 border border-purple-600 rounded-lg">
              <p className="text-purple-300">
                Selected Exam: <strong>{selectedExam.name}</strong>
              </p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 max-h-96 overflow-y-auto">
              {US_STATES.map((state) => (
                <button
                  key={state.code}
                  onClick={() => handleStateSelect(state)}
                  className="p-3 bg-gray-700/50 rounded-lg border border-gray-600 hover:border-purple-500 hover:bg-purple-500/10 transition-all text-center"
                >
                  <div className="font-medium text-white text-sm">{state.code}</div>
                  <div className="text-xs text-gray-400">{state.name}</div>
                </button>
              ))}
            </div>
          </div>
        )}

        {step === 3 && selectedExam && selectedState && (
          <div>
            <h3 className="text-xl font-bold mb-6 flex items-center">
              <User className="w-6 h-6 mr-3 text-purple-400" />
              Student Information
            </h3>
            <div className="mb-6 space-y-2">
              <div className="p-4 bg-purple-600/20 border border-purple-600 rounded-lg">
                <p className="text-purple-300">
                  <strong>{selectedExam.name}</strong> in <strong>{selectedState.name}</strong>
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-300 mb-2">Full Name *</label>
                <input
                  type="text"
                  value={studentInfo.name}
                  onChange={(e) => setStudentInfo(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Enter your full name"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">College/Training Course</label>
                <input
                  type="text"
                  value={studentInfo.college}
                  onChange={(e) => setStudentInfo(prev => ({ ...prev, college: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Enter your college or training course"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-gray-300 mb-2">Address *</label>
                <input
                  type="text"
                  value={studentInfo.address}
                  onChange={(e) => setStudentInfo(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Enter your street address"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">City *</label>
                <input
                  type="text"
                  value={studentInfo.city}
                  onChange={(e) => setStudentInfo(prev => ({ ...prev, city: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Enter your city"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">State *</label>
                <input
                  type="text"
                  value={studentInfo.state}
                  onChange={(e) => setStudentInfo(prev => ({ ...prev, state: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Enter your state"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2">Country</label>
                <select
                  value={studentInfo.country}
                  onChange={(e) => setStudentInfo(prev => ({ ...prev, country: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {countries.map((country) => (
                    <option key={country} value={country}>{country}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">Zip Code *</label>
                <input
                  type="text"
                  value={studentInfo.zipCode}
                  onChange={(e) => setStudentInfo(prev => ({ ...prev, zipCode: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Enter your zip code"
                  required
                />
              </div>
            </div>

            {error && (
              <div className="mt-4 p-3 bg-red-900/50 border border-red-500 rounded-lg">
                <p className="text-red-300 text-sm">{error}</p>
              </div>
            )}

            <button
              onClick={handleStudentInfoSubmit}
              className="w-full mt-6 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              Continue to Exam Date
            </button>
          </div>
        )}

        {step === 4 && selectedExam && selectedState && (
          <div>
            <h3 className="text-xl font-bold mb-6 flex items-center">
              <Calendar className="w-6 h-6 mr-3 text-purple-400" />
              Select Your Exam Date
            </h3>
            <div className="mb-6 space-y-2">
              <div className="p-4 bg-purple-600/20 border border-purple-600 rounded-lg">
                <p className="text-purple-300">
                  <strong>{selectedExam.name}</strong> in <strong>{selectedState.name}</strong>
                </p>
                <p className="text-purple-200 text-sm mt-1">
                  Student: {studentInfo.name} • {studentInfo.city}, {studentInfo.state}
                </p>
              </div>
            </div>
            <div className="max-w-md mx-auto">
              <label className="block text-gray-300 mb-2">Exam Date</label>
              <input
                type="date"
                value={examDate}
                onChange={(e) => setExamDate(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
                className="w-full p-4 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <div className="mt-4 p-4 bg-yellow-600/20 border border-yellow-600 rounded-lg">
                <p className="text-yellow-300 text-sm">
                  <strong>Important:</strong> Your study features will be deactivated after your exam date.
                  You can reactivate them by reporting your exam results.
                </p>
              </div>
              <button
                onClick={handleDateSelect}
                disabled={!examDate}
                className="w-full mt-6 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Continue to Payment
              </button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ExamRegistration;