import { useState, useEffect } from 'react';
import { useUser } from '@civic/auth/react';
import { CivicUser, civicAuth } from '../lib/civic';
import { checkUserSubscription } from '../lib/supabase';

export interface CivicAuthState {
  user: CivicUser | null;
  loading: boolean;
  hasActiveSubscription: boolean;
  subscriptionLoading: boolean;
  isVerified: boolean;
}

export const useCivicAuth = () => {
  const [authState, setAuthState] = useState<CivicAuthState>({
    user: null,
    loading: true,
    hasActiveSubscription: false,
    subscriptionLoading: true,
    isVerified: false,
  });

  const { user, isLoading, error } = useUser();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check for existing session first
        const existingUser = civicAuth.getCurrentUser();
        if (existingUser) {
          setAuthState(prev => ({
            ...prev,
            user: existingUser,
            loading: false,
            isVerified: existingUser.isVerified,
          }));
          await checkSubscription(existingUser.id);
          return;
        }

        // Handle Civic authentication
        if (isLoading) {
          setAuthState(prev => ({ ...prev, loading: true }));
          return;
        }

        if (error) {
          setAuthState(prev => ({
            ...prev,
            loading: false,
            subscriptionLoading: false,
            isVerified: false,
          }));
          return;
        }

        if (user) {
          const civicUser: CivicUser = {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            isVerified: true, // Assume user from useUser is verified
            verificationLevel: 'kyc', // Adjust based on your Civic setup
            verifiedAt: new Date(),
          };
          civicAuth.setUser(civicUser);
          setAuthState(prev => ({
            ...prev,
            user: civicUser,
            loading: false,
            isVerified: true,
          }));
          await checkSubscription(civicUser.id);
        } else {
          civicAuth.signOut();
          setAuthState(prev => ({
            ...prev,
            loading: false,
            subscriptionLoading: false,
            isVerified: false,
          }));
        }
      } catch (err) {
        console.error('Auth initialization failed:', err);
        setAuthState(prev => ({
          ...prev,
          loading: false,
          subscriptionLoading: false,
          isVerified: false,
        }));
      }
    };

    initializeAuth();
  }, [user, isLoading, error]);

  const checkSubscription = async (userId: string) => {
    try {
      setAuthState(prev => ({ ...prev, subscriptionLoading: true }));
      const subscription = await checkUserSubscription(userId);
      setAuthState(prev => ({
        ...prev,
        hasActiveSubscription: !!subscription,
        subscriptionLoading: false,
      }));
    } catch (error) {
      console.error('Error checking subscription:', error);
      setAuthState(prev => ({
        ...prev,
        hasActiveSubscription: false,
        subscriptionLoading: false,
      }));
    }
  };

  const signOut = async () => {
    try {
      await civicAuth.signOut();
      setAuthState({
        user: null,
        loading: false,
        hasActiveSubscription: false,
        subscriptionLoading: false,
        isVerified: false,
      });
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  };

  return {
    ...authState,
    signOut,
    refreshSubscription: () => {
      if (authState.user) {
        checkSubscription(authState.user.id);
      }
    },
  };
};
