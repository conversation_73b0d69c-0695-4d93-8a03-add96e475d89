import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Upload, FileText, Copy, Trash2, Plus } from 'lucide-react';

interface StudySection {
  id: string;
  title: string;
  content: string;
}

interface StudyMaterialUploadProps {
  onContentReady: (content: string, sections: StudySection[]) => void;
}

const StudyMaterialUpload: React.FC<StudyMaterialUploadProps> = ({ onContentReady }) => {
  const [sections, setSections] = useState<StudySection[]>([
    { id: '1', title: 'Section 1', content: '' }
  ]);
  const [selectedDifficulty, setSelectedDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');

  const addSection = () => {
    const newSection: StudySection = {
      id: Date.now().toString(),
      title: `Section ${sections.length + 1}`,
      content: ''
    };
    setSections([...sections, newSection]);
  };

  const removeSection = (id: string) => {
    if (sections.length > 1) {
      setSections(sections.filter(section => section.id !== id));
    }
  };

  const updateSection = (id: string, field: 'title' | 'content', value: string) => {
    setSections(sections.map(section => 
      section.id === id ? { ...section, [field]: value } : section
    ));
  };

  const handleSubmit = () => {
    const validSections = sections.filter(section => section.content.trim());
    if (validSections.length === 0) {
      alert('Please add content to at least one section');
      return;
    }

    const combinedContent = validSections
      .map(section => `${section.title}:\n${section.content}`)
      .join('\n\n');

    onContentReady(combinedContent, validSections);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-4xl mx-auto"
    >
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
          Add Your Study Material
        </h2>
        <p className="text-gray-500 text-lg">
          Copy and paste your study content by section or topic
        </p>
      </div>

      {/* Difficulty Selection */}
      <div className="mb-8">
        <h3 className="text-xl font-bold mb-4 text-white">Select Difficulty Level</h3>
        <div className="grid grid-cols-3 gap-4">
          {[
            { level: 'easy', color: 'green', description: 'Basic concepts, longer time per question' },
            { level: 'medium', color: 'yellow', description: 'Standard difficulty, moderate timing' },
            { level: 'hard', color: 'red', description: 'Advanced concepts, shorter time limits' }
          ].map(({ level, color, description }) => (
            <button
              key={level}
              onClick={() => setSelectedDifficulty(level as 'easy' | 'medium' | 'hard')}
              className={`p-4 rounded-lg border-2 transition-all ${
                selectedDifficulty === level
                  ? `border-${color}-500 bg-${color}-500/20`
                  : 'border-gray-600 bg-gray-700/50 hover:border-gray-500'
              }`}
            >
              <h4 className={`font-semibold mb-2 ${
                selectedDifficulty === level 
                  ? `text-${color}-400` 
                  : 'text-gray-300'
              } capitalize`}>
                {level}
              </h4>
              <p className="text-sm text-gray-400">{description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Study Sections */}
      <div className="space-y-6">
        {sections.map((section, index) => (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <input
                type="text"
                value={section.title}
                onChange={(e) => updateSection(section.id, 'title', e.target.value)}
                className="text-lg font-semibold bg-transparent border-none text-white focus:outline-none focus:ring-2 focus:ring-purple-500 rounded px-2 py-1"
                placeholder="Section title..."
              />
              
              <div className="flex items-center space-x-2">
                {sections.length > 1 && (
                  <button
                    onClick={() => removeSection(section.id)}
                    className="p-2 text-red-400 hover:bg-red-500/20 rounded-lg transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            <textarea
              value={section.content}
              onChange={(e) => updateSection(section.id, 'content', e.target.value)}
              placeholder="Paste your study material here..."
              className="w-full h-40 p-4 bg-gray-900/50 border border-gray-600 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            />

            <div className="mt-3 text-sm text-gray-500">
              {section.content.length} characters
            </div>
          </motion.div>
        ))}

        <button
          onClick={addSection}
          className="w-full p-4 border-2 border-dashed border-gray-600 rounded-lg hover:border-purple-500 hover:bg-purple-500/10 transition-all flex items-center justify-center space-x-2 text-gray-400 hover:text-purple-400"
        >
          <Plus className="w-5 h-5" />
          <span>Add Another Section</span>
        </button>
      </div>

      <div className="mt-8 text-center">
        <button
          onClick={handleSubmit}
          className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg text-lg font-semibold hover:scale-105 transition-all shadow-lg"
        >
          Start Studying
        </button>
      </div>
    </motion.div>
  );
};

export default StudyMaterialUpload;